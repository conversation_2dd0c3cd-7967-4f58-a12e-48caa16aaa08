#!/usr/bin/env python3
"""
简化的使用示例脚本
"""

import os
from process_csv_with_chatgpt import process_csv_file

def main():
    # 配置参数
    input_file = "new_csv/content_CogAgent.csv"  # 输入文件
    output_file = "new_csv/content_CogAgent_with_chatgpt.csv"  # 输出文件
    
    # 从环境变量获取API密钥
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("错误：请设置环境变量 OPENAI_API_KEY")
        print("例如：export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # 处理参数
    delay = 2.0  # API调用间隔（秒）
    start_row = 0  # 从第一行开始
    end_row = 5  # 只处理前5行作为测试，设置为None处理全部
    
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"API延迟: {delay}秒")
    print(f"处理范围: 第{start_row+1}行到第{end_row}行")
    print("开始处理...")
    
    # 调用处理函数
    process_csv_file(
        input_file=input_file,
        output_file=output_file,
        api_key=api_key,
        delay=delay,
        start_row=start_row,
        end_row=end_row
    )
    
    print("处理完成！")

if __name__ == "__main__":
    main()
